from typing import List, Dict, Any, Optional, Tuple
import logging
from datetime import datetime
from app.database import db
from app.models import (
    ClinicalDataQuery, StatisticsQuery, Visit, Doctor, Unit, Patient,
    Statistics, TopItem, DiagnosisTrends, DoctorActivity, DiagnosisByAge,
    ClinicalDataResponse, DiagnosisTrendDataset
)

logger = logging.getLogger(__name__)

class ClinicalService:
    
    # Base query from the provided SQL
    BASE_QUERY = """
        WITH cteopdclinicA AS (
            SELECT
                vpc.*,
                vpd.diagnoses
            FROM vw_patcomplaints vpc
            INNER JOIN vw_patdiagnosis vpd
                ON vpc.visitid = vpd.visitid
                AND vpc.opdipd = vpd.opdipd
                AND vpc.patientid = vpd.patientid
                AND vpc.unitid = vpd.unitid
            WHERE vpc.opdipd = 0
        )
        SELECT
            A.*,
            dp.gender,
            multiIf(
                (toYear(today()) - toYear(dp.dateofbirth)) < 16, '<16',
                (toYear(today()) - toYear(dp.dateofbirth)) BETWEEN 17 AND 25, '17-25',
                (toYear(today()) - toYear(dp.dateofbirth)) BETWEEN 26 AND 40, '26-40',
                (toYear(today()) - toYear(dp.dateofbirth)) BETWEEN 41 AND 55, '41-55',
                (toYear(today()) - toYear(dp.dateofbirth)) BETWEEN 56 AND 65, '56-65',
                '>65'
            ) AS age_group,
            dd.doctor_id,
            concat('Dr. ', dd.firstname, ' ', dd.lastname) AS Doctorname,
            NULL AS medications,
            NULL AS investigations
        FROM cteopdclinicA A
        INNER JOIN dim_encounter de ON A.visitid = de.encounterid
        INNER JOIN dim_patient dp ON dp.patient_id = A.patientid
        INNER JOIN dim_doctor dd ON dd.doctor_id = de.doctormodalityid
        -- Uncomment the line below if needed
        -- WHERE A.complaints = 'Follow up'
    """
    
    @staticmethod
    def get_age_from_age_group(age_group: str) -> int:
        """Convert age group to actual age (middle of range)"""
        age_ranges = {
            '<16': (0, 15),
            '17-25': (17, 25),
            '26-40': (26, 40),
            '41-55': (41, 55),
            '56-65': (56, 65),
            '>65': (66, 150)
        }
        
        if age_group in age_ranges:
            min_age, max_age = age_ranges[age_group]
            return (min_age + max_age) // 2
        return 30  # Default age
    
    @staticmethod
    def transform_row_to_visit(row: Dict[str, Any]) -> Visit:
        """Transform database row to Visit model"""
        # Generate a mock date since the database doesn't have a date column
        mock_date = datetime(2024, 10, 6, 18, 30, 0)
        
        # Normalize gender value
        gender_value = row['gender']
        if gender_value == 'FEMALE':
            gender_value = 'Female'
        elif gender_value == 'MALE':
            gender_value = 'Male'
        elif gender_value not in ['Male', 'Female', 'Other']:
            gender_value = 'Other'

        return Visit(
            id=str(row['visitid']),
            patient=Patient(
                id=str(row['patientid']),
                age=ClinicalService.get_age_from_age_group(row['age_group']),
                gender=gender_value
            ),
            doctor=Doctor(
                id=str(row['doctor_id']),
                name=row['doctorname']
            ),
            date=mock_date.isoformat(),
            chiefComplaint=row.get('complaints', ''),
            diagnosis=row.get('diagnoses', ''),
            medications=row.get('medications', '').split(',') if row.get('medications') else [],
            investigations=row.get('investigations', '').split(',') if row.get('investigations') else [],
            referred=False  # Mock referral data
        )
    
    @staticmethod
    def get_clinical_data(query: ClinicalDataQuery) -> ClinicalDataResponse:
        """Get clinical data with filtering and pagination"""
        try:
            where_conditions = []
            query_params = []
            param_index = 1
            
            # Add date filters using vpc.addeddatetime column
            if query.start_date:
                where_conditions.append("A.addeddatetime >= %s")
                query_params.append(query.start_date)

            if query.end_date:
                where_conditions.append("A.addeddatetime <= %s")
                query_params.append(query.end_date)

            # Add unit filter
            if query.unit_id:
                where_conditions.append("A.unitid = %s")
                query_params.append(query.unit_id)

            # Add doctor filter
            if query.doctor_id:
                where_conditions.append("dd.doctor_id = %s")
                query_params.append(query.doctor_id)

            # Add diagnosis filter
            if query.diagnosis:
                where_conditions.append("A.diagnoses ILIKE %s")
                query_params.append(f"%{query.diagnosis}%")
            
            # Add age group filter
            if query.age_group:
                age_group_condition = """
                    multiIf(
                        (toYear(today()) - toYear(dp.dateofbirth)) < 16, '<16',
                        (toYear(today()) - toYear(dp.dateofbirth)) BETWEEN 17 AND 25, '17-25',
                        (toYear(today()) - toYear(dp.dateofbirth)) BETWEEN 26 AND 40, '26-40',
                        (toYear(today()) - toYear(dp.dateofbirth)) BETWEEN 41 AND 55, '41-55',
                        (toYear(today()) - toYear(dp.dateofbirth)) BETWEEN 56 AND 65, '56-65',
                        '>65'
                    ) = %s
                """
                where_conditions.append(age_group_condition)
                query_params.append(query.age_group.value)

            # Add gender filter
            if query.gender:
                where_conditions.append("dp.gender = %s")
                query_params.append(query.gender.value)
            
            # Build the complete query
            full_query = ClinicalService.BASE_QUERY
            if where_conditions:
                full_query += f" WHERE {' AND '.join(where_conditions)}"
            
            # Add ordering
            full_query += " ORDER BY A.visitid DESC"
            
            # Count query for total
            count_query = f"SELECT COUNT(*) as total FROM ({full_query}) as counted_data"
            count_result = db.execute_single_query(count_query, query_params)
            total = count_result['total'] if count_result else 0
            
            # Add pagination to main query
            offset = (query.page - 1) * query.limit
            full_query += " LIMIT %s OFFSET %s"
            query_params.extend([query.limit, offset])
            
            # Execute main query
            results = db.execute_query(full_query, query_params)
            
            # Transform results to Visit objects
            visits = [ClinicalService.transform_row_to_visit(row) for row in results]
            
            # Get doctors
            doctors = ClinicalService.get_doctors()
            
            return ClinicalDataResponse(
                visits=visits,
                doctors=doctors,
                totalCount=total,
                page=query.page,
                limit=query.limit
            )
            
        except Exception as e:
            logger.error(f"Error fetching clinical data: {e}")
            raise
    
    @staticmethod
    def get_doctors() -> List[Doctor]:
        """Get list of all doctors"""
        try:
            query = """
                SELECT DISTINCT dd.doctor_id as id, concat('Dr. ', dd.firstname, ' ', dd.lastname) as name
                FROM dim_doctor dd
                INNER JOIN dim_encounter de ON dd.doctor_id = de.doctormodalityid
                ORDER BY name
            """
            
            results = db.execute_query(query)
            return [Doctor(id=str(row['id']), name=row['name']) for row in results]
            
        except Exception as e:
            logger.error(f"Error fetching doctors: {e}")
            raise
    
    @staticmethod
    def get_units() -> List[Unit]:
        """Get list of all units"""
        try:
            query = """
                SELECT DISTINCT
                  unitid as id,
                  CASE
                    WHEN unitid = 1 THEN 'Emergency Department'
                    WHEN unitid = 2 THEN 'Internal Medicine'
                    WHEN unitid = 3 THEN 'Cardiology'
                    WHEN unitid = 4 THEN 'Pediatrics'
                    WHEN unitid = 5 THEN 'Surgery'
                    WHEN unitid = 6 THEN 'Orthopedics'
                    WHEN unitid = 7 THEN 'Neurology'
                    WHEN unitid = 8 THEN 'Oncology'
                    ELSE concat('Unit ', toString(unitid))
                  END as name
                FROM vw_patcomplaints
                WHERE unitid IS NOT NULL
                  AND unitid > 0
                ORDER BY unitid
            """
            
            results = db.execute_query(query)
            return [Unit(id=str(row['id']), name=row['name']) for row in results]
            
        except Exception as e:
            logger.error(f"Error fetching units from database, returning mock data: {e}")
            # Return mock data as fallback
            return [
                Unit(id='1', name='Emergency Department'),
                Unit(id='2', name='Internal Medicine'),
                Unit(id='3', name='Cardiology'),
                Unit(id='4', name='Pediatrics'),
                Unit(id='5', name='Surgery'),
                Unit(id='6', name='Orthopedics'),
                Unit(id='7', name='Neurology'),
                Unit(id='8', name='Oncology')
            ]

    @staticmethod
    def get_statistics(query: StatisticsQuery) -> Statistics:
        """Get clinical statistics"""
        try:
            # Build where conditions for filtering
            where_conditions = []
            query_params = []

            if query.start_date:
                where_conditions.append("A.addeddatetime >= %s")
                query_params.append(query.start_date)

            if query.end_date:
                where_conditions.append("A.addeddatetime <= %s")
                query_params.append(query.end_date)

            if query.unit_id:
                where_conditions.append("A.unitid = %s")
                query_params.append(query.unit_id)

            if query.doctor_id:
                where_conditions.append("dd.doctor_id = %s")
                query_params.append(query.doctor_id)

            where_clause = ""
            if where_conditions:
                where_clause = "WHERE " + " AND ".join(where_conditions)

            # Get total patients
            total_patients_query = f"""
                SELECT COUNT(DISTINCT A.patientid) as total_patients
                FROM ({ClinicalService.BASE_QUERY}) A
                LEFT JOIN dim_doctor dd ON A.doctor_id = dd.doctor_id
                {where_clause}
            """
            total_patients_result = db.execute_single_query(total_patients_query, query_params)
            total_patients = total_patients_result['total_patients'] if total_patients_result else 0

            # Get total visits
            total_visits_query = f"""
                SELECT COUNT(*) as total_visits
                FROM ({ClinicalService.BASE_QUERY}) A
                LEFT JOIN dim_doctor dd ON A.doctor_id = dd.doctor_id
                {where_clause}
            """
            total_visits_result = db.execute_single_query(total_visits_query, query_params)
            total_visits = total_visits_result['total_visits'] if total_visits_result else 0

            # Get total doctors
            total_doctors_query = f"""
                SELECT COUNT(DISTINCT dd.doctor_id) as total_doctors
                FROM ({ClinicalService.BASE_QUERY}) A
                LEFT JOIN dim_doctor dd ON A.doctor_id = dd.doctor_id
                {where_clause}
            """
            total_doctors_result = db.execute_single_query(total_doctors_query, query_params)
            total_doctors = total_doctors_result['total_doctors'] if total_doctors_result else 0

            # Get referral rate (assuming referred field exists or calculate based on some criteria)
            referral_rate = 12.5  # Default for now, would need actual referral data

            # Get polypharmacy count (fallback since medications field is NULL in base query)
            # Since the base query has NULL AS medications, polypharmacy count is 0
            polypharmacy_count = 0

            # Get top complaint
            top_complaint_query = f"""
                SELECT A.complaints as complaint, COUNT(*) as count
                FROM ({ClinicalService.BASE_QUERY}) A
                LEFT JOIN dim_doctor dd ON A.doctor_id = dd.doctor_id
                {where_clause}
                GROUP BY A.complaints
                ORDER BY count DESC
                LIMIT 1
            """
            top_complaint_result = db.execute_single_query(top_complaint_query, query_params)
            top_complaint = TopItem(
                name=top_complaint_result['complaint'] if top_complaint_result else "N/A",
                count=top_complaint_result['count'] if top_complaint_result else 0
            )

            # Get top diagnosis
            top_diagnosis_query = f"""
                SELECT A.diagnoses as diagnosis, COUNT(*) as count
                FROM ({ClinicalService.BASE_QUERY}) A
                LEFT JOIN dim_doctor dd ON A.doctor_id = dd.doctor_id
                {where_clause}
                GROUP BY A.diagnoses
                ORDER BY count DESC
                LIMIT 1
            """
            top_diagnosis_result = db.execute_single_query(top_diagnosis_query, query_params)
            top_diagnosis = TopItem(
                name=top_diagnosis_result['diagnosis'] if top_diagnosis_result else "N/A",
                count=top_diagnosis_result['count'] if top_diagnosis_result else 0
            )

            # Get top medication (fallback since medications field is NULL in base query)
            # Since the base query has NULL AS medications, we'll provide a fallback
            top_medication = TopItem(
                name="N/A (No medication data available)",
                count=0
            )

            # Get average investigations (fallback since investigations field is NULL in base query)
            # Since the base query has NULL AS investigations, average is 0.0
            avg_investigations = "0.0"

            # Get most frequent complaint-diagnosis pair
            frequent_pair_query = f"""
                SELECT concat(A.complaints, ' → ', A.diagnoses) as pair, COUNT(*) as count
                FROM ({ClinicalService.BASE_QUERY}) A
                LEFT JOIN dim_doctor dd ON A.doctor_id = dd.doctor_id
                {where_clause}
                GROUP BY A.complaints, A.diagnoses
                ORDER BY count DESC
                LIMIT 1
            """
            frequent_pair_result = db.execute_single_query(frequent_pair_query, query_params)
            most_frequent_pair = frequent_pair_result['pair'] if frequent_pair_result else "N/A"

            return Statistics(
                totalPatients=total_patients,
                totalVisits=total_visits,
                totalDoctors=total_doctors,
                referralRate=referral_rate,
                polypharmacyCount=polypharmacy_count,
                topComplaint=top_complaint,
                topDiagnosis=top_diagnosis,
                topMedication=top_medication,
                avgInvestigations=avg_investigations,
                mostFrequentPair=most_frequent_pair
            )
        except Exception as e:
            logger.error(f"Error fetching statistics: {e}")
            raise

    @staticmethod
    def get_diagnosis_trends(query: StatisticsQuery) -> DiagnosisTrends:
        """Get diagnosis trends over time"""
        try:
            # Build where conditions for filtering
            where_conditions = []
            query_params = []

            if query.start_date:
                where_conditions.append("A.addeddatetime >= %s")
                query_params.append(query.start_date)

            if query.end_date:
                where_conditions.append("A.addeddatetime <= %s")
                query_params.append(query.end_date)

            if query.unit_id:
                where_conditions.append("A.unitid = %s")
                query_params.append(query.unit_id)

            if query.doctor_id:
                where_conditions.append("dd.doctor_id = %s")
                query_params.append(query.doctor_id)

            where_clause = ""
            if where_conditions:
                where_clause = "WHERE " + " AND ".join(where_conditions)

            # Get diagnosis trends by month
            trends_query = f"""
                SELECT
                    formatDateTime(A.addeddatetime, '%b') as month,
                    toMonth(A.addeddatetime) as month_num,
                    A.diagnoses as diagnosis,
                    COUNT(*) as count
                FROM ({ClinicalService.BASE_QUERY}) A
                LEFT JOIN dim_doctor dd ON A.doctor_id = dd.doctor_id
                {where_clause}
                GROUP BY month, month_num, A.diagnoses
                ORDER BY month_num, count DESC
            """

            results = db.execute_query(trends_query, query_params)

            # Process results to create chart data
            months = {}
            diagnosis_data = {}

            for row in results:
                month = row['month']
                diagnosis = row['diagnosis']
                count = row['count']

                if month not in months:
                    months[month] = row['month_num']

                if diagnosis not in diagnosis_data:
                    diagnosis_data[diagnosis] = {}

                diagnosis_data[diagnosis][month] = count

            # Sort months by month number
            sorted_months = sorted(months.keys(), key=lambda x: months[x])

            # Get top 5 diagnoses by total count
            diagnosis_totals = {}
            for diagnosis, month_data in diagnosis_data.items():
                diagnosis_totals[diagnosis] = sum(month_data.values())

            top_diagnoses = sorted(diagnosis_totals.keys(), key=lambda x: diagnosis_totals[x], reverse=True)[:5]

            # Create datasets
            datasets = []
            for diagnosis in top_diagnoses:
                data = []
                for month in sorted_months:
                    data.append(diagnosis_data[diagnosis].get(month, 0))

                datasets.append(DiagnosisTrendDataset(
                    label=diagnosis,
                    data=data
                ))

            # If no data found, return mock data
            if not datasets:
                return DiagnosisTrends(
                    labels=["Jan", "Feb", "Mar", "Apr", "May", "Jun"],
                    datasets=[
                        DiagnosisTrendDataset(label="No Data", data=[0, 0, 0, 0, 0, 0])
                    ]
                )

            return DiagnosisTrends(
                labels=sorted_months,
                datasets=datasets
            )
        except Exception as e:
            logger.error(f"Error fetching diagnosis trends: {e}")
            # Return fallback data
            return DiagnosisTrends(
                labels=["Jan", "Feb", "Mar", "Apr", "May", "Jun"],
                datasets=[
                    DiagnosisTrendDataset(label="Error", data=[0, 0, 0, 0, 0, 0])
                ]
            )

    @staticmethod
    def get_doctor_activity(query: StatisticsQuery) -> List[DoctorActivity]:
        """Get doctor activity metrics"""
        try:
            # Build where conditions for filtering
            where_conditions = []
            query_params = []

            if query.start_date:
                where_conditions.append("A.addeddatetime >= %s")
                query_params.append(query.start_date)

            if query.end_date:
                where_conditions.append("A.addeddatetime <= %s")
                query_params.append(query.end_date)

            if query.unit_id:
                where_conditions.append("A.unitid = %s")
                query_params.append(query.unit_id)

            if query.doctor_id:
                where_conditions.append("dd.doctor_id = %s")
                query_params.append(query.doctor_id)

            where_clause = ""
            if where_conditions:
                where_clause = "WHERE " + " AND ".join(where_conditions)

            # Get doctor activity metrics
            activity_query = f"""
                SELECT
                    concat('Dr. ', dd.firstname, ' ', dd.lastname) as doctor_name,
                    COUNT(DISTINCT A.patientid) as unique_patients,
                    COUNT(*) as total_visits,
                    ROUND(COUNT(DISTINCT A.patientid) /
                          if(COUNT(DISTINCT toDate(A.addeddatetime)) = 0, 1, COUNT(DISTINCT toDate(A.addeddatetime))), 1) as avg_patients_per_day,
                    A.diagnoses as diagnosis,
                    COUNT(*) OVER (PARTITION BY dd.doctor_id, A.diagnoses) as diagnosis_count,
                    ROW_NUMBER() OVER (PARTITION BY dd.doctor_id ORDER BY COUNT(*) DESC) as diagnosis_rank,
                    0.0 as avg_medications
                FROM ({ClinicalService.BASE_QUERY}) A
                LEFT JOIN dim_doctor dd ON A.doctor_id = dd.doctor_id
                {where_clause}
                GROUP BY dd.doctor_id, dd.firstname, dd.lastname, A.diagnoses
                ORDER BY unique_patients DESC
            """

            results = db.execute_query(activity_query, query_params)

            # Process results to get doctor activity
            doctor_data = {}

            for row in results:
                doctor_name = row['doctor_name']

                if doctor_name not in doctor_data:
                    doctor_data[doctor_name] = {
                        'avg_patients': str(row['avg_patients_per_day']) if row['avg_patients_per_day'] else "0.0",
                        'top_diagnosis': None,
                        'avg_meds': "0.0",
                        'total_patients': row['unique_patients']
                    }

                # Get top diagnosis (first one with rank 1)
                if row['diagnosis_rank'] == 1:
                    doctor_data[doctor_name]['top_diagnosis'] = row['diagnosis']
                    doctor_data[doctor_name]['avg_meds'] = f"{float(row['avg_medications']):.1f}" if row['avg_medications'] else "0.0"

            # Convert to list and sort by total patients
            activity_list = []
            for doctor_name, data in doctor_data.items():
                activity_list.append(DoctorActivity(
                    name=doctor_name,
                    avgPatients=data['avg_patients'],
                    topDiagnosis=data['top_diagnosis'] or "N/A",
                    avgMeds=data['avg_meds']
                ))

            # Sort by total patients (extract from avg_patients for now)
            activity_list.sort(key=lambda x: float(x.avg_patients), reverse=True)

            # Return top 10 doctors
            return activity_list[:10] if activity_list else [
                DoctorActivity(name="No Data", avgPatients="0.0", topDiagnosis="N/A", avgMeds="0.0")
            ]

        except Exception as e:
            logger.error(f"Error fetching doctor activity: {e}")
            # Return fallback data
            return [
                DoctorActivity(name="Error", avgPatients="0.0", topDiagnosis="N/A", avgMeds="0.0")
            ]

    @staticmethod
    def get_diagnosis_by_age(query: StatisticsQuery) -> DiagnosisByAge:
        """Get diagnosis distribution by age groups"""
        try:
            # Build where conditions for filtering
            where_conditions = []
            query_params = []

            if query.start_date:
                where_conditions.append("A.addeddatetime >= %s")
                query_params.append(query.start_date)

            if query.end_date:
                where_conditions.append("A.addeddatetime <= %s")
                query_params.append(query.end_date)

            if query.unit_id:
                where_conditions.append("A.unitid = %s")
                query_params.append(query.unit_id)

            if query.doctor_id:
                where_conditions.append("dd.doctor_id = %s")
                query_params.append(query.doctor_id)

            where_clause = ""
            if where_conditions:
                where_clause = "WHERE " + " AND ".join(where_conditions)

            # Get diagnosis by age group
            age_diagnosis_query = f"""
                SELECT
                    A.age_group,
                    A.diagnoses as diagnosis,
                    COUNT(*) as count
                FROM ({ClinicalService.BASE_QUERY}) A
                LEFT JOIN dim_doctor dd ON A.doctor_id = dd.doctor_id
                {where_clause}
                GROUP BY A.age_group, A.diagnoses
                ORDER BY A.age_group, count DESC
            """

            results = db.execute_query(age_diagnosis_query, query_params)

            # Define age group order
            age_groups = ["<16", "17-25", "26-40", "41-55", "56-65", ">65"]

            # Process results
            diagnosis_data = {}

            for row in results:
                age_group = row['age_group']
                diagnosis = row['diagnosis']
                count = row['count']

                if diagnosis not in diagnosis_data:
                    diagnosis_data[diagnosis] = {age: 0 for age in age_groups}

                if age_group in age_groups:
                    diagnosis_data[diagnosis][age_group] = count

            # Get top 5 diagnoses by total count
            diagnosis_totals = {}
            for diagnosis, age_data in diagnosis_data.items():
                diagnosis_totals[diagnosis] = sum(age_data.values())

            top_diagnoses = sorted(diagnosis_totals.keys(), key=lambda x: diagnosis_totals[x], reverse=True)[:5]

            # Create datasets
            datasets = []
            for diagnosis in top_diagnoses:
                data = []
                for age_group in age_groups:
                    data.append(diagnosis_data[diagnosis][age_group])

                datasets.append(DiagnosisTrendDataset(
                    label=diagnosis,
                    data=data
                ))

            # If no data found, return mock data
            if not datasets:
                return DiagnosisByAge(
                    labels=age_groups,
                    datasets=[
                        DiagnosisTrendDataset(label="No Data", data=[0, 0, 0, 0, 0, 0])
                    ]
                )

            return DiagnosisByAge(
                labels=age_groups,
                datasets=datasets
            )

        except Exception as e:
            logger.error(f"Error fetching diagnosis by age: {e}")
            # Return fallback data
            return DiagnosisByAge(
                labels=["<16", "17-25", "26-40", "41-55", "56-65", ">65"],
                datasets=[
                    DiagnosisTrendDataset(label="Error", data=[0, 0, 0, 0, 0, 0])
                ]
            )
