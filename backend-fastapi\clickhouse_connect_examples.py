#!/usr/bin/env python3
"""
Examples demonstrating clickhouse_connect features and usage patterns
"""

import sys
import os
from datetime import datetime, date
from decimal import Decimal

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

from database import db

def example_basic_queries():
    """Basic query examples"""
    print("=== Basic Query Examples ===")
    
    # Simple SELECT
    result = db.execute_query("SELECT 1 as number, 'hello' as text")
    print(f"Simple query: {result}")
    
    # Query with functions
    result = db.execute_query("""
        SELECT 
            now() as current_time,
            today() as current_date,
            version() as clickhouse_version
    """)
    print(f"Functions query: {result}")
    
    # Single result query
    result = db.execute_single_query("SELECT count() as total_tables FROM system.tables WHERE database = 'default'")
    print(f"Single result: {result}")

def example_parameterized_queries():
    """Parameterized query examples"""
    print("\n=== Parameterized Query Examples ===")
    
    # Using named parameters (recommended for clickhouse_connect)
    result = db.execute_query("""
        SELECT 
            {user_id:UInt32} as user_id,
            {user_name:String} as user_name,
            {created_date:Date} as created_date,
            {score:Float64} as score
    """, params={
        'user_id': 123,
        'user_name': 'John Doe',
        'created_date': date.today(),
        'score': 95.5
    })
    print(f"Named parameters: {result}")
    
    # Query with IN clause
    result = db.execute_query("""
        SELECT name FROM system.databases 
        WHERE name IN {db_names:Array(String)}
    """, params={
        'db_names': ['default', 'system']
    })
    print(f"IN clause with array: {result}")

def example_data_types():
    """Examples of different ClickHouse data types"""
    print("\n=== Data Types Examples ===")
    
    result = db.execute_query("""
        SELECT 
            toInt32(42) as int_value,
            toFloat64(3.14159) as float_value,
            toString('text') as string_value,
            toDate('2024-01-01') as date_value,
            toDateTime('2024-01-01 12:00:00') as datetime_value,
            [1, 2, 3] as array_value,
            map('key1', 'value1', 'key2', 'value2') as map_value
    """)
    print(f"Data types: {result}")

def example_aggregations():
    """Aggregation examples"""
    print("\n=== Aggregation Examples ===")
    
    # Table statistics
    result = db.execute_query("""
        SELECT 
            database,
            count() as table_count,
            sum(total_rows) as total_rows,
            sum(total_bytes) as total_bytes
        FROM system.tables 
        WHERE database IN ('default', 'system')
        GROUP BY database
        ORDER BY database
    """)
    print(f"Database statistics: {result}")

def example_server_info():
    """Server information examples"""
    print("\n=== Server Information ===")
    
    # Using the new utility method
    server_info = db.get_server_info()
    print(f"Server info: {server_info}")
    
    # System information
    result = db.execute_query("""
        SELECT 
            hostName() as hostname,
            uptime() as uptime_seconds,
            version() as version,
            timezone() as timezone
    """)
    print(f"System info: {result}")

def example_insert_data():
    """Example of inserting data (be careful with this!)"""
    print("\n=== Insert Data Example ===")
    
    # Note: This is just an example - be very careful with inserts in production!
    try:
        # First, let's create a temporary table for testing
        db.execute_query("""
            CREATE TABLE IF NOT EXISTS test_table (
                id UInt32,
                name String,
                created_date Date,
                score Float64
            ) ENGINE = Memory
        """)
        
        # Sample data to insert
        sample_data = [
            {'id': 1, 'name': 'Alice', 'created_date': date.today(), 'score': 95.5},
            {'id': 2, 'name': 'Bob', 'created_date': date.today(), 'score': 87.2},
            {'id': 3, 'name': 'Charlie', 'created_date': date.today(), 'score': 92.8}
        ]
        
        # Insert using the new utility method
        success = db.execute_insert('test_table', sample_data)
        if success:
            print("✅ Data inserted successfully")
            
            # Verify the insert
            result = db.execute_query("SELECT * FROM test_table ORDER BY id")
            print(f"Inserted data: {result}")
        
        # Clean up - drop the test table
        db.execute_query("DROP TABLE IF EXISTS test_table")
        print("✅ Test table cleaned up")
        
    except Exception as e:
        print(f"❌ Insert example failed: {e}")

def example_clinical_data():
    """Examples using actual clinical data"""
    print("\n=== Clinical Data Examples ===")
    
    try:
        # Get patient count by gender
        result = db.execute_query("""
            SELECT 
                gender,
                count() as patient_count
            FROM vw_patdiagnosis 
            GROUP BY gender
            ORDER BY patient_count DESC
            LIMIT 10
        """)
        print(f"Patients by gender: {result}")
        
        # Get top diagnoses
        result = db.execute_query("""
            SELECT 
                diagnoses,
                count() as diagnosis_count
            FROM vw_patdiagnosis 
            WHERE diagnoses != ''
            GROUP BY diagnoses
            ORDER BY diagnosis_count DESC
            LIMIT 5
        """)
        print(f"Top diagnoses: {result}")
        
        # Get doctor statistics
        result = db.execute_query("""
            SELECT 
                Doctorname,
                count() as patient_count,
                count(DISTINCT patientid) as unique_patients
            FROM vw_patdiagnosis 
            WHERE Doctorname != ''
            GROUP BY Doctorname
            ORDER BY patient_count DESC
            LIMIT 5
        """)
        print(f"Doctor statistics: {result}")
        
    except Exception as e:
        print(f"❌ Clinical data examples failed: {e}")

def main():
    """Run all examples"""
    print("ClickHouse Connect Examples")
    print("=" * 50)
    
    try:
        # Test connection first
        if not db.test_connection():
            print("❌ Database connection failed!")
            return False
        
        print("✅ Database connection successful!")
        
        # Run examples
        example_basic_queries()
        example_parameterized_queries()
        example_data_types()
        example_aggregations()
        example_server_info()
        example_insert_data()
        example_clinical_data()
        
        print("\n" + "=" * 50)
        print("🎉 All examples completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Examples failed: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
