#!/usr/bin/env python3
"""
Comprehensive ClickHouse connection diagnostics
"""

import requests
import json
import sys
import os

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

from config import settings

def test_ping():
    """Test ClickHouse ping endpoint"""
    print("1. Testing ClickHouse ping endpoint...")
    
    try:
        url = f"http://{settings.db_host}:{settings.db_port}/ping"
        print(f"   URL: {url}")
        
        response = requests.get(url, timeout=10)
        print(f"   ✅ Ping successful! Status: {response.status_code}")
        print(f"   Response: {response.text}")
        return True
    except Exception as e:
        print(f"   ❌ Ping failed: {e}")
        return False

def test_no_auth():
    """Test connection without authentication"""
    print("\n2. Testing connection without authentication...")
    
    try:
        url = f"http://{settings.db_host}:{settings.db_port}/"
        response = requests.post(
            url,
            data="SELECT 1",
            headers={'Content-Type': 'text/plain'},
            timeout=10
        )
        print(f"   ✅ No-auth successful! Status: {response.status_code}")
        print(f"   Response: {response.text}")
        return True
    except Exception as e:
        print(f"   ❌ No-auth failed: {e}")
        return False

def test_basic_auth():
    """Test with basic authentication"""
    print("\n3. Testing with basic authentication...")
    
    try:
        url = f"http://{settings.db_host}:{settings.db_port}/"
        auth = (settings.db_user, settings.db_password)
        
        print(f"   URL: {url}")
        print(f"   Auth: {settings.db_user}:{'*' * len(settings.db_password)}")
        
        response = requests.post(
            url,
            auth=auth,
            data="SELECT 1",
            headers={'Content-Type': 'text/plain'},
            timeout=10
        )
        print(f"   ✅ Basic auth successful! Status: {response.status_code}")
        print(f"   Response: {response.text}")
        return True
    except Exception as e:
        print(f"   ❌ Basic auth failed: {e}")
        return False

def test_with_database():
    """Test with database parameter"""
    print("\n4. Testing with database parameter...")
    
    try:
        url = f"http://{settings.db_host}:{settings.db_port}/"
        auth = (settings.db_user, settings.db_password)
        
        response = requests.post(
            url,
            auth=auth,
            data="SELECT 1",
            headers={'Content-Type': 'text/plain'},
            params={'database': settings.db_name},
            timeout=10
        )
        print(f"   ✅ Database param successful! Status: {response.status_code}")
        print(f"   Response: {response.text}")
        return True
    except Exception as e:
        print(f"   ❌ Database param failed: {e}")
        return False

def test_json_format():
    """Test with JSON format"""
    print("\n5. Testing with JSON format...")
    
    try:
        url = f"http://{settings.db_host}:{settings.db_port}/"
        auth = (settings.db_user, settings.db_password)
        
        response = requests.post(
            url,
            auth=auth,
            data="SELECT 1 as test_value",
            headers={'Content-Type': 'text/plain'},
            params={'database': settings.db_name, 'default_format': 'JSONEachRow'},
            timeout=10
        )
        print(f"   ✅ JSON format successful! Status: {response.status_code}")
        print(f"   Response: {response.text}")
        
        # Try to parse JSON
        for line in response.text.strip().split('\n'):
            if line:
                data = json.loads(line)
                print(f"   Parsed JSON: {data}")
        return True
    except Exception as e:
        print(f"   ❌ JSON format failed: {e}")
        return False

def test_show_databases():
    """Test SHOW DATABASES query"""
    print("\n6. Testing SHOW DATABASES...")
    
    try:
        url = f"http://{settings.db_host}:{settings.db_port}/"
        auth = (settings.db_user, settings.db_password)
        
        response = requests.post(
            url,
            auth=auth,
            data="SHOW DATABASES",
            headers={'Content-Type': 'text/plain'},
            timeout=10
        )
        print(f"   ✅ SHOW DATABASES successful! Status: {response.status_code}")
        print(f"   Available databases:")
        for db in response.text.strip().split('\n'):
            if db:
                print(f"     - {db}")
        return True
    except Exception as e:
        print(f"   ❌ SHOW DATABASES failed: {e}")
        return False

def test_alternative_ports():
    """Test alternative ClickHouse ports"""
    print("\n7. Testing alternative ports...")
    
    alternative_ports = [8123, 9000, 9009]
    
    for port in alternative_ports:
        if port == settings.db_port:
            continue  # Skip the configured port as we already tested it
            
        try:
            url = f"http://{settings.db_host}:{port}/ping"
            print(f"   Testing port {port}...")
            
            response = requests.get(url, timeout=5)
            print(f"   ✅ Port {port} responds! Status: {response.status_code}")
            return True
        except Exception as e:
            print(f"   ❌ Port {port} failed: {e}")
    
    return False

def main():
    """Run all diagnostic tests"""
    print("ClickHouse Connection Diagnostics")
    print("=" * 50)
    print(f"Host: {settings.db_host}")
    print(f"Port: {settings.db_port}")
    print(f"Database: {settings.db_name}")
    print(f"User: {settings.db_user}")
    print("=" * 50)
    
    tests = [
        test_ping,
        test_no_auth,
        test_basic_auth,
        test_with_database,
        test_json_format,
        test_show_databases,
        test_alternative_ports
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print("\n" + "=" * 50)
    print(f"Diagnostic tests passed: {passed}/{total}")
    
    if passed > 0:
        print("🎉 Some connectivity was established!")
        print("💡 Check the successful tests above for working connection parameters.")
    else:
        print("⚠️  No connectivity established.")
        print("💡 Possible issues:")
        print("   - ClickHouse server is not running")
        print("   - Firewall blocking connections")
        print("   - Wrong host/port configuration")
        print("   - Authentication issues")
    
    return 0 if passed > 0 else 1

if __name__ == "__main__":
    sys.exit(main())
