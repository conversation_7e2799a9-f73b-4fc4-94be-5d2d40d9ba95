#!/usr/bin/env python3
"""
Test script to verify ClickHouse connection using clickhouse_connect
"""

import sys
import os

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

from database import db
from config import settings

def test_connection():
    """Test basic database connection"""
    print("Testing ClickHouse database connection with clickhouse_connect...")
    print(f"Host: {settings.db_host}")
    print(f"Port: {settings.db_port}")
    print(f"Database: {settings.db_name}")
    print(f"User: {settings.db_user}")
    print(f"Secure: {settings.db_secure}")
    print("-" * 50)
    
    try:
        result = db.test_connection()
        if result:
            print("✅ Database connection successful!")
            return True
        else:
            print("❌ Database connection failed!")
            return False
    except Exception as e:
        print(f"❌ Database connection error: {e}")
        return False

def test_simple_query():
    """Test a simple query"""
    print("\nTesting simple query...")
    try:
        result = db.execute_query("SELECT 1 as test_value, 'hello' as message")
        print(f"✅ Simple query successful: {result}")
        return True
    except Exception as e:
        print(f"❌ Simple query failed: {e}")
        return False

def test_show_databases():
    """Test SHOW DATABASES query"""
    print("\nTesting SHOW DATABASES...")
    try:
        result = db.execute_query("SHOW DATABASES")
        print(f"✅ SHOW DATABASES successful!")
        print("Available databases:")
        for row in result:
            print(f"  - {row.get('name', row)}")
        return True
    except Exception as e:
        print(f"❌ SHOW DATABASES failed: {e}")
        return False

def test_show_tables():
    """Test SHOW TABLES query"""
    print("\nTesting SHOW TABLES...")
    try:
        result = db.execute_query("SHOW TABLES")
        print(f"✅ SHOW TABLES successful!")
        print(f"Found {len(result)} tables:")
        for row in result:
            print(f"  - {row.get('name', row)}")
        return True
    except Exception as e:
        print(f"❌ SHOW TABLES failed: {e}")
        return False

def test_parameterized_query():
    """Test parameterized query"""
    print("\nTesting parameterized query...")
    try:
        # Test with parameters
        result = db.execute_query(
            "SELECT {param1:Int32} as number, {param2:String} as text", 
            params={'param1': 42, 'param2': 'test'}
        )
        print(f"✅ Parameterized query successful: {result}")
        return True
    except Exception as e:
        print(f"❌ Parameterized query failed: {e}")
        return False

def main():
    """Run all tests"""
    print("ClickHouse Connect Test Suite")
    print("=" * 50)
    
    tests = [
        test_connection,
        test_simple_query,
        test_show_databases,
        test_show_tables,
        test_parameterized_query
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"❌ Test {test.__name__} crashed: {e}")
    
    print("\n" + "=" * 50)
    print(f"Tests passed: {passed}/{total}")
    
    if passed == total:
        print("🎉 All tests passed!")
        return True
    else:
        print("⚠️ Some tests failed!")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
