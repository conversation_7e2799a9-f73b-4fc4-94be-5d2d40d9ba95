# ClickHouse Connect Migration

This document describes the migration from HTTP-based ClickHouse connectivity to using the `clickhouse_connect` library.

## What Changed

### Before (HTTP-based)
- Used `requests` library to make HTTP calls to ClickHouse
- Manual JSON parsing of responses
- Basic parameter substitution with string formatting
- Limited error handling and connection management

### After (clickhouse_connect)
- Native ClickHouse client using `clickhouse_connect`
- Automatic data type conversion
- Proper parameterized queries
- Better connection management and SSL support
- Optimized insert operations

## Benefits of clickhouse_connect

### 1. **Better Performance**
- Native protocol support (faster than HTTP)
- Optimized data serialization/deserialization
- Connection pooling and reuse

### 2. **Improved Data Type Handling**
- Automatic conversion between ClickHouse and Python types
- Support for complex types (arrays, maps, tuples)
- Proper handling of dates, timestamps, and decimals

### 3. **Enhanced Security**
- Built-in SSL/TLS support
- Certificate-based authentication
- Secure connection verification

### 4. **Better Error Handling**
- Detailed error messages from ClickHouse
- Proper exception types
- Connection state management

### 5. **Parameterized Queries**
- Safe parameter binding (prevents SQL injection)
- Support for named parameters
- Type-safe parameter handling

## Configuration Changes

### New Settings in config.py
```python
# ClickHouse connection settings
db_secure: bool = os.getenv("DB_SECURE", "true").lower() == "true"
db_verify: bool = os.getenv("DB_VERIFY", "true").lower() == "true"
db_ca_cert: str = os.getenv("DB_CA_CERT", "")
db_client_cert: str = os.getenv("DB_CLIENT_CERT", "")
db_client_key: str = os.getenv("DB_CLIENT_KEY", "")
```

### Environment Variables
You can now configure these additional settings in your `.env` file:
```bash
# SSL/TLS settings
DB_SECURE=true          # Use secure connection (HTTPS/TLS)
DB_VERIFY=true          # Verify SSL certificates
DB_CA_CERT=             # Path to CA certificate file
DB_CLIENT_CERT=         # Path to client certificate file
DB_CLIENT_KEY=          # Path to client private key file
```

## Code Examples

### Basic Query
```python
from app.database import db

# Simple query
result = db.execute_query("SELECT 1 as test")
print(result)  # [{'test': 1}]
```

### Parameterized Query
```python
# Named parameters (recommended)
result = db.execute_query("""
    SELECT {user_id:UInt32} as id, {name:String} as name
""", params={'user_id': 123, 'name': 'John'})
```

### Data Types
```python
# ClickHouse types are automatically converted to Python types
result = db.execute_query("""
    SELECT 
        toDate('2024-01-01') as date_val,
        toDateTime('2024-01-01 12:00:00') as datetime_val,
        [1, 2, 3] as array_val,
        map('key', 'value') as map_val
""")
# Returns proper Python datetime.date, datetime.datetime, list, dict objects
```

### Insert Data
```python
# Optimized bulk insert
data = [
    {'id': 1, 'name': 'Alice', 'score': 95.5},
    {'id': 2, 'name': 'Bob', 'score': 87.2}
]
success = db.execute_insert('my_table', data)
```

## Migration Checklist

- [x] Install `clickhouse_connect` package
- [x] Update `config.py` with new SSL/connection settings
- [x] Rewrite `database.py` to use clickhouse_connect
- [x] Update connection initialization
- [x] Replace HTTP requests with native client calls
- [x] Add new utility methods (insert, server_info, close)
- [x] Test basic connectivity
- [x] Test existing application functionality
- [x] Create examples and documentation

## Testing

### Run Connection Tests
```bash
# Test the new connection
python test_clickhouse_connect.py

# Test existing application compatibility
python test_clickhouse_connection.py

# Run comprehensive examples
python clickhouse_connect_examples.py
```

### Verify FastAPI Application
```bash
# Start the FastAPI server
python -m app.main

# Check health endpoint
curl http://localhost:8000/health
```

## Troubleshooting

### Connection Issues
1. **SSL/TLS Problems**: Check `DB_SECURE` and `DB_VERIFY` settings
2. **Certificate Issues**: Verify certificate paths in `DB_CA_CERT`, etc.
3. **Port Issues**: Ensure you're using the correct port (8443 for HTTPS, 9000 for native)

### Query Issues
1. **Parameter Binding**: Use named parameters with type hints: `{param:Type}`
2. **Data Types**: Check ClickHouse documentation for proper type names
3. **Column Names**: Ensure column names exist in your tables/views

## Performance Tips

1. **Use Native Port**: Consider using port 9000 (native) instead of 8443 (HTTPS) for better performance
2. **Batch Inserts**: Use `execute_insert()` for bulk data operations
3. **Connection Reuse**: The client automatically handles connection pooling
4. **Parameterized Queries**: Always use parameters instead of string formatting

## Next Steps

1. **Monitor Performance**: Compare query execution times before/after migration
2. **Update Documentation**: Update any API documentation that references the old HTTP method
3. **Consider Native Port**: Evaluate switching to the native protocol port (9000) for even better performance
4. **Add Monitoring**: Implement connection health monitoring and metrics

## Resources

- [clickhouse_connect Documentation](https://clickhouse.com/docs/en/integrations/python)
- [ClickHouse Data Types](https://clickhouse.com/docs/en/sql-reference/data-types/)
- [ClickHouse SQL Reference](https://clickhouse.com/docs/en/sql-reference/)
