#!/usr/bin/env python3
"""
Network connectivity test for ClickHouse server
"""

import socket
import sys
import os

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

from config import settings

def test_tcp_connection(host, port, timeout=5):
    """Test TCP connection to host:port"""
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(timeout)
        result = sock.connect_ex((host, port))
        sock.close()
        return result == 0
    except Exception as e:
        print(f"Socket error: {e}")
        return False

def test_dns_resolution(host):
    """Test DNS resolution"""
    try:
        ip = socket.gethostbyname(host)
        print(f"✅ DNS resolution successful: {host} -> {ip}")
        return True, ip
    except Exception as e:
        print(f"❌ DNS resolution failed: {e}")
        return False, None

def main():
    """Run network connectivity tests"""
    print("Network Connectivity Test for ClickHouse")
    print("=" * 45)
    print(f"Target: {settings.db_host}:{settings.db_port}")
    print()
    
    # Test DNS resolution
    print("1. Testing DNS resolution...")
    dns_ok, resolved_ip = test_dns_resolution(settings.db_host)
    
    if not dns_ok:
        print("❌ Cannot resolve hostname. Check if the hostname is correct.")
        return 1
    
    # Test TCP connection
    print(f"\n2. Testing TCP connection to {resolved_ip}:{settings.db_port}...")
    if test_tcp_connection(resolved_ip, settings.db_port):
        print(f"✅ TCP connection successful!")
        print("💡 The server is reachable. The issue might be with HTTP/authentication.")
    else:
        print(f"❌ TCP connection failed!")
        print("💡 Possible issues:")
        print("   - Server is not running")
        print("   - Port is blocked by firewall")
        print("   - Wrong port number")
        print("   - Network routing issues")
        
        # Test common ClickHouse ports
        print(f"\n3. Testing alternative ClickHouse ports...")
        common_ports = [8123, 9000, 9009, 9004]
        
        for port in common_ports:
            if port == settings.db_port:
                continue
            print(f"   Testing port {port}...")
            if test_tcp_connection(resolved_ip, port):
                print(f"   ✅ Port {port} is open! Consider using this port.")
            else:
                print(f"   ❌ Port {port} is closed.")
        
        return 1
    
    print("\n" + "=" * 45)
    print("🎉 Network connectivity looks good!")
    print("💡 If the application still fails, check:")
    print("   - ClickHouse HTTP interface is enabled")
    print("   - Authentication credentials")
    print("   - Database permissions")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
